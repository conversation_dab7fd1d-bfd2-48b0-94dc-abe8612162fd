"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import IconUpload from "@/shared/icons/Upload";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import DeleteFileModal from "./DeleteFileModal";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
interface MultipleHotelFilesProps {
  hotelToken: string | undefined;
  filePath: string;
  docType: string;
  name: string;
  hotelData: HotelDataApiTypes;
}

const MultipleHotelFiles: FC<MultipleHotelFilesProps> = ({
  hotelToken,
  filePath,
  docType,
  name,
  hotelData,
}) => {
  const translate = useTranslations("HotelFiles");
  const [fileObjects, setFileObjects] = useState<File[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { uploadFileHandler } = useHotelInformationsActions();

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setFileObjects((prevFiles) => [...prevFiles, ...Array.from(files)]);
    } else {
      console.warn("No files selected");
    }
  };

  // filter files based on the selected docType
  const filteredFiles = hotelData?.files?.filter(
    (file: any) => file?.doctype === docType
  );

  // sort files from the most recently uploaded to the earliest uploaded
  const sortedData = filteredFiles?.sort(
    (a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const renderFileNames = () => {
    return fileObjects?.map((file, index) => (
      <div key={index} className="mt-2 flex items-center">
        <span className="text-sm max-md:w-60 max-md:text-xs">{file?.name}</span>
      </div>
    ));
  };

  const renderSortedFileNames = () => {
    return sortedData?.map((file: any, index: number) => (
      <div
        key={index}
        className="relative flex items-center border-b border-neutral-300 pb-1"
      >
        <a
          className="hover:underline max-md:w-60"
          href={file?.src}
          target="_blank"
        >
          <span className="text-sm max-md:text-xs">{file?.originalname}</span>
        </a>
        {file?._id && (
          <DeleteFileModal fileId={file?._id} hotelToken={hotelToken} />
        )}
      </div>
    ));
  };

  return (
    <form
      onSubmit={(event) =>
        uploadFileHandler(
          event,
          fileObjects,
          hotelToken,
          setLoading,
          filePath,
          docType,
          setDisabled
        )
      }
      className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-5"
    >
      <div className="w-full md:basis-1/2">
        <div className="my-2 text-sm font-medium capitalize text-neutral-700 dark:text-neutral-300">
          {name}
        </div>
        <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
          <div className="flex flex-col items-center space-y-1 text-center">
            <div className="text-sm text-neutral-6000 dark:text-neutral-300">
              <label
                htmlFor={`file-upload-${docType}`}
                className="group relative cursor-pointer rounded-md font-medium text-secondary-6000"
              >
                <div className="mb-1 flex justify-center">
                  <IconUpload className="size-6 text-secondary-6000 group-hover:text-primary-700" />
                </div>
                <span className="group-hover:text-primary-700 group-hover:underline">
                  {name} {translate("uploadButtonText")}
                </span>
                <input
                  id={`file-upload-${docType}`}
                  name={`file-upload-${docType}`}
                  type="file"
                  className="sr-only"
                  accept="application/pdf"
                  multiple={true}
                  onChange={handleFileChange}
                />
              </label>
              {/* <p className="pl-1">or drag and drop</p> */}
            </div>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              PDF
            </p>
            <div className="mt-4 font-semibold">{renderFileNames()}</div>
            <div className="mt-4 space-y-3">{renderSortedFileNames()}</div>
          </div>
        </div>
      </div>

      <Button
        className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
        disabled={disabled || fileObjects.length === 0 ? true : false}
        type="submit"
      >
        {loading ? <LoadingSpinner /> : translate("saveButtonText")}
      </Button>
    </form>
  );
};

export default MultipleHotelFiles;
