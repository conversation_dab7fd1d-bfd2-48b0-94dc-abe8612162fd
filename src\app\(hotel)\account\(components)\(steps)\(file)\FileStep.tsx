import React from "react";
import type { FC } from "react";
import SingleHotelFile from "../../../hotel-informations/(components)/(hotelFiles)/SingleHotelFile";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface FileStepProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

const FileStep: FC<FileStepProps> = ({ hotelToken, hotelData }) => {
  const translate = useTranslations("FileStep");
  return (
    <>
      <p className="text-xl font-semibold mb-5">{translate("hotelFiles")}</p>
      {hotelData && (
        <div className="mt-5 space-y-5">
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData._id}/files/`}
            docType="license"
            name={translate("license")}
            hotelData={hotelData}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData._id}/files/`}
            docType="taxCertificate"
            name={translate("taxCertificate")}
            hotelData={hotelData}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData._id}/files/`}
            docType="identificationDocumentFront"
            name={translate("identificationDocumentFront")}
            hotelData={hotelData}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData._id}/files/`}
            docType="identificationDocumentBack"
            name={translate("identificationDocumentBack")}
            hotelData={hotelData}
          />
          {hotelData.propertyType === "petHotel" && (
            <SingleHotelFile
              hotelToken={hotelToken}
              filePath={`hotel/${hotelData._id}/files/`}
              docType="veterinaryContract"
              name={translate("veterinaryContract")}
              hotelData={hotelData}
            />
          )}
        </div>
      )}
    </>
  );
};

export default FileStep;
