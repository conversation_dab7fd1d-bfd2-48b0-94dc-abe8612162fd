"use client";
import type { <PERSON> } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import IconUpload from "@/shared/icons/Upload";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import DeleteFileModal from "./DeleteFileModal";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
interface SingleHotelFileProps {
  hotelToken: string | undefined;
  filePath: string;
  docType: string;
  name: string;
  hotelData: HotelDataApiTypes;
  fullWidth?: boolean;
}

const SingleHotelFile: FC<SingleHotelFileProps> = ({
  hotelToken,
  filePath,
  docType,
  name,
  hotelData,
  fullWidth = false,
}) => {
  const translate = useTranslations("HotelFiles");
  const [fileObject, setFileObject] = useState<FileList | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { uploadFileHandler } = useHotelInformationsActions();

  // filter files based on the selected docType
  const filteredFiles = hotelData?.files?.filter(
    (file: any) => file?.doctype === docType
  );

  // sort files from the most recently uploaded to the earliest uploaded
  const sortedData = filteredFiles?.sort(
    (a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
  const inputDisabled = filteredFiles.length > 0;
  return (
    <>
      <div className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-5">
        <div className={`${fullWidth ? "w-[450px] mx-auto" : "w-full md:basis-1/2"}`}>
          <div className="my-2 text-sm font-medium capitalize text-neutral-700 dark:text-neutral-300">
            {name}
          </div>
          <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
            <div className="flex flex-col items-center space-y-1 text-center">
              <div className="text-sm text-neutral-6000 dark:text-neutral-300">
                {/* Display a label, icon, and input for the user to upload a file */}
                <label
                  htmlFor={`file-upload-${docType}`}
                  className={`group relative rounded-md font-medium text-secondary-6000 ${inputDisabled ? "cursor-not-allowed" : "cursor-pointer"}`}
                >
                  <div className="mb-1 flex justify-center">
                    <IconUpload className="size-6 text-secondary-6000 group-hover:text-primary-700" />
                  </div>
                  <span className="group-hover:text-primary-700 group-hover:underline">
                    {name} {translate("uploadButtonText")}
                  </span>
                  <input
                    disabled={inputDisabled}
                    id={`file-upload-${docType}`}
                    name={`file-upload-${docType}`}
                    type="file"
                    className="sr-only"
                    accept="application/pdf"
                    multiple={false}
                    onChange={(event) => {
                      const files = event.target.files;
                      if (files) {
                        setFileObject(files);
                        uploadFileHandler(
                          event,
                          files,
                          hotelToken,
                          setLoading,
                          filePath,
                          docType,
                          setDisabled
                        );
                      }
                    }}
                  />
                </label>
                {/* <p className="pl-1">or drag and drop</p> */}
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                PDF
              </p>
              {/* Display the selected file name if a file is uploaded, 
                otherwise, show a link to the most recent uploaded file */}
              {fileObject ? (
                <div>
                  <p className="text-sm max-md:w-60 max-md:text-xs">
                    {fileObject[0]?.name}
                  </p>
                </div>
              ) : (
                <div className="relative text-sm max-md:w-60 max-md:text-xs">
                  <a
                    className="hover:underline"
                    href={sortedData[0]?.src}
                    target="_blank"
                  >
                    {sortedData[0]?.originalname}
                  </a>
                  {sortedData[0]?._id && (
                    <DeleteFileModal
                      fileId={sortedData[0]?._id}
                      hotelToken={hotelToken}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SingleHotelFile;
