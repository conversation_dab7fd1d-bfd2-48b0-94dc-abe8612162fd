"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import SingleHotelFile from "../../../hotel-informations/(components)/(hotelFiles)/SingleHotelFile";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
import { Check, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface FileStepProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

interface FileStepData {
  docType: string;
  name: string;
  required: boolean;
  condition?: (hotelData: HotelDataApiTypes) => boolean;
}

const FileStep: FC<FileStepProps> = ({ hotelToken, hotelData }) => {
  const translate = useTranslations("FileStep");
  const [currentStep, setCurrentStep] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const fileSteps: FileStepData[] = [
    {
      docType: "license",
      name: translate("license"),
      required: true,
    },
    {
      docType: "taxCertificate",
      name: translate("taxCertificate"),
      required: true,
    },
    {
      docType: "identificationDocumentFront",
      name: translate("identificationDocumentFront"),
      required: true,
    },
    {
      docType: "identificationDocumentBack",
      name: translate("identificationDocumentBack"),
      required: true,
    },
    {
      docType: "veterinaryContract",
      name: translate("veterinaryContract"),
      required: true,
      condition: (hotelData) => hotelData.propertyType === "petHotel",
    },
  ];

  const filteredSteps = fileSteps.filter(
    (step) => !step.condition || step.condition(hotelData)
  );

  // Completed steps'i hesapla (useEffect kullanma)
  const completedSteps = React.useMemo(() => {
    if (!hotelData?.files) return new Set<number>();

    const newCompletedSteps = new Set<number>();
    filteredSteps.forEach((step, index) => {
      const isUploaded = hotelData.files.some((file: any) => file?.doctype === step.docType);
      if (isUploaded) {
        newCompletedSteps.add(index);
      }
    });
    return newCompletedSteps;
  }, [hotelData?.files, filteredSteps]);

  // İlk yüklemede yüklenmemiş step'e geç (sadece bir kez)
  useEffect(() => {
    if (!hasInitialized && filteredSteps.length > 0) {
      // İlk yüklenmemiş step'i bul
      for (let i = 0; i < filteredSteps.length; i++) {
        const step = filteredSteps[i];
        const isUploaded = hotelData?.files?.some((file: any) => file?.doctype === step.docType);
        if (!isUploaded) {
          setCurrentStep(i);
          setHasInitialized(true);
          return;
        }
      }
      // Hepsi yüklüyse ilk step'e git
      setCurrentStep(0);
      setHasInitialized(true);
    }
  }, [filteredSteps.length, hasInitialized]); // Sadece filteredSteps.length ve hasInitialized

  const nextStep = () => {
    if (currentStep < filteredSteps.length - 1 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const prevStep = () => {
    if (currentStep > 0 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(currentStep - 1);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex !== currentStep && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(stepIndex);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const currentStepData = filteredSteps[currentStep];

  return (
    <>
      <div className="mb-8">
        <p className="text-xl font-semibold mb-2">{translate("hotelFiles")}</p>
        <p className="text-sm text-neutral-600 dark:text-neutral-400">
          {currentStep + 1} / {filteredSteps.length} - {currentStepData?.name}
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {filteredSteps.map((step, index) => (
            <div key={step.docType} className="flex items-center">
              <button
                onClick={() => goToStep(index)}
                disabled={isTransitioning}
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                  index === currentStep
                    ? "bg-[#d5e4f2] text-black scale-110"
                    : completedSteps.has(index)
                      ? "bg-green-500 text-white"
                      : "bg-neutral-200 text-neutral-600 hover:bg-neutral-300"
                } ${isTransitioning ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
              >
                {completedSteps.has(index) && index !== currentStep ? (
                  <Check className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </button>
              {index < filteredSteps.length && (
                <div
                  className={`w-12 h-1 mx-2 transition-all duration-700 ${
                    index === currentStep
                      ? "bg-[#d5e4f2]"
                      : completedSteps.has(index)
                        ? "bg-green-500"
                        : "bg-neutral-200"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Current Step Content */}
      {hotelData && currentStepData && (
        <div className="relative overflow-hidden">
          <div
            key={currentStep}
            className={`transform transition-all duration-700 ease-out ${
              isTransitioning
                ? "opacity-50 scale-95"
                : "opacity-100 scale-100 animate-in slide-in-from-bottom-8 fade-in duration-700"
            }`}
          >
            <SingleHotelFile
              hotelToken={hotelToken}
              filePath={`hotel/${hotelData._id}/files/`}
              docType={currentStepData.docType}
              name={currentStepData.name}
              hotelData={hotelData}
              fullWidth={true}
            />
          </div>
        </div>
      )}

      {/* Status Message */}
      <div className="flex justify-center mt-8">
        {!completedSteps.has(currentStep) && !isTransitioning && (
          <span className="text-sm text-amber-600 bg-amber-50 px-4 py-3 rounded-lg animate-pulse">
            Bu dosyayı yükleyin
          </span>
        )}
        {completedSteps.has(currentStep) && !isTransitioning && (
          <span className="text-sm text-green-600 bg-green-50 px-4 py-3 rounded-lg flex items-center gap-2">
            <Check className="w-4 h-4" />
            Bu dosya başarıyla yüklendi
          </span>
        )}
        {isTransitioning && (
          <span className="text-sm text-blue-600 bg-blue-50 px-4 py-3 rounded-lg flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            Geçiş yapılıyor...
          </span>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-6">
        {currentStep > 0 ? (
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={isTransitioning}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Önceki
          </Button>
        ) : (
          <div></div>
        )}

        <Button
          onClick={nextStep}
          disabled={
            currentStep === filteredSteps.length - 1 ||
            isTransitioning ||
            !completedSteps.has(currentStep)
          }
          className="bg-secondary-6000 hover:bg-secondary-700 flex items-center gap-2"
        >
          Sonraki
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      {/* Success Message */}
      {completedSteps.size === filteredSteps.length && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg animate-in fade-in duration-1000">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <Check className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-green-800">Tebrikler!</h4>
              <p className="text-sm text-green-600">
                Tüm gerekli dosyalar başarıyla yüklendi. Dosya yükleme işlemi
                tamamlandı.
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FileStep;
