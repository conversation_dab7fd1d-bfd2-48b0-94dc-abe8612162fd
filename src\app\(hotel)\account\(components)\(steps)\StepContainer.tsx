import React from "react";
import type { FC } from "react";
import FileStep from "./(file)/FileStep";
import BillingStep from "./(billing)/BillingStep";
import PolicyStep from "./(policy)/PolicyStep";
import HotelPetTypes from "../../hotel-informations/(components)/(hotelPetTypes)/HotelPetTypes";
import HotelFeatures from "../../hotel-informations/(components)/(hotelFeatures)/HotelFeatures";
import HotelMap from "../../hotel-informations/(components)/(hotelAddress)/HotelMap";
import IconCheck from "@/shared/icons/Check";
import HotelSuccessImage from "@/components/HotelSuccessImage";
import HotelPhotosContainer from "../../hotel-informations/(components)/(hotelPhotos)/HotelPhotosContainer";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
interface StepContainerProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
  checkSubMerchant: boolean;
}

const StepContainer: FC<StepContainerProps> = ({
  hotelData,
  hotelToken,
  checkSubMerchant,
}) => {
  const stepHandler = (hotelData: HotelDataApiTypes) => {
    if (hotelData?.files?.length < 6) return 1;

    if (!checkSubMerchant) return 2;

    if (hotelData?.acceptedPetTypes?.length === 0) return 3;

    if (hotelData?.hotelFeatures?.length === 0) return 4;

    if (hotelData?.images?.length === 0) return 5;

    // if (!hotelData?.data?.googleMapUrl) return 6;

    if (!hotelData?.policy?.checkOutTime) return 6;

    return 8;
  };

  const stepValue = stepHandler(hotelData);

  return (
    <div className="md:mt-10">
      {stepValue === 1 && (
        <FileStep hotelData={hotelData} hotelToken={hotelToken} />
      )}
      {stepValue === 2 && <BillingStep hotelToken={hotelToken} />}
      {stepValue === 3 && (
        <HotelPetTypes hotelData={hotelData} hotelToken={hotelToken} />
      )}
      {stepValue === 4 && (
        <HotelFeatures hotelData={hotelData} hotelToken={hotelToken} />
      )}
      {stepValue === 5 && (
        <HotelPhotosContainer hotelData={hotelData} hotelToken={hotelToken} />
      )}
      {/* {stepValue === 6 && (
        <HotelMap hotelData={hotelData?.data} hotelToken={hotelToken} />
      )} */}
      {stepValue === 6 && <PolicyStep hotelToken={hotelToken} />}
      {stepValue === 8 && (
        <div className="flex flex-col justify-center items-center gap-5 text-center font-medium text-lg">
          <div className="flex items-center relative">
            <HotelSuccessImage className="size-60" />
            <IconCheck className="size-12 rounded-full bg-green-500 p-2 text-white absolute -right-7 bottom-5" />
          </div>
          <p>
            Tebrikler! Otel kaydınızı başarıyla oluşturdunuz. Kayıt bilgileriniz
            şu anda onay sürecindedir. Danışmanımız en kısa sürede sizinle
            iletişime geçerek süreci tamamlamanız için gerekli yönlendirmeleri
            yapacaktır. İlginiz için teşekkür ederiz.
          </p>
        </div>
      )}
    </div>
  );
};

export default StepContainer;
